/***************************************************
# File Name:    server.c
# Author:       wang
# Mail:         <EMAIL>
# Created Time: 2025年08月14日 星期四 17时45分27秒
# Modified:     2025年08月16日 - 添加多线程和完整考勤系统功能
****************************************************/

#include <stdio.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <pthread.h>
#include <sqlite3.h>

#define PORT 8080
#define BUFFER_SIZE 1024
#define MAX_CLIENTS 100

// 标准工作时间设置
#define STANDARD_START_HOUR 9    // 标准上班时间：9:00
#define STANDARD_START_MIN 0
#define STANDARD_END_HOUR 18     // 标准下班时间：18:00
#define STANDARD_END_MIN 0
#define STANDARD_WORK_HOURS 8.0  // 标准工作时长：8小时

// 客户端信息结构
typedef struct {
    int socket_fd;
    struct sockaddr_in address;
} client_info_t;

// 记录日志函数
void log_operation(const char *operation, const char *user, const char *details) {
    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "无法打开数据库记录日志: %s\n", sqlite3_errmsg(db));
        return;
    }

    time_t now;
    time(&now);
    char *timestamp = ctime(&now);
    timestamp[strlen(timestamp) - 1] = '\0'; // 移除换行符

    const char *sql = "INSERT INTO logs (operation, user, timestamp, details) VALUES (?, ?, ?, ?)";
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc == SQLITE_OK) {
        sqlite3_bind_text(stmt, 1, operation, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 2, user, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 3, timestamp, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 4, details, -1, SQLITE_STATIC);
        sqlite3_step(stmt);
        sqlite3_finalize(stmt);
    }

    sqlite3_close(db);
}

// 时间比较辅助函数
typedef struct {
    int hour;
    int minute;
    int second;
} time_info_t;
// 解析时间字符串 "HH:MM:SS"
time_info_t parse_time(const char *time_str) {
    time_info_t t = {0, 0, 0};
    if (time_str) {
        sscanf(time_str, "%d:%d:%d", &t.hour, &t.minute, &t.second);
    }
    return t;
}
// 将时间转换为分钟数（从00:00开始计算）
int time_to_minutes(time_info_t t) {
    return t.hour * 60 + t.minute;
}
// 计算两个时间之间的分钟差
int time_diff_minutes(time_info_t t1, time_info_t t2) {
    return time_to_minutes(t1) - time_to_minutes(t2);
}

// 检查是否迟到
bool is_late_checkin(const char *checkin_time, int *late_minutes) {
    time_info_t checkin = parse_time(checkin_time);
    time_info_t standard = {STANDARD_START_HOUR, STANDARD_START_MIN, 0};

    int diff = time_diff_minutes(checkin, standard);
    if (diff > 0) {
        *late_minutes = diff;
        return true;
    }
    *late_minutes = 0;
    return false;
}

// 检查是否早退
bool is_early_leave(const char *checkout_time, int *early_minutes) {
    time_info_t checkout = parse_time(checkout_time);
    time_info_t standard = {STANDARD_END_HOUR, STANDARD_END_MIN, 0};

    int diff = time_diff_minutes(standard, checkout);
    if (diff > 0) {
        *early_minutes = diff;
        return true;
    }
    *early_minutes = 0;
    return false;
}

// 计算实际加班时长(考虑标准工作时间)
double calculate_overtime_hours(const char *checkin_time, const char *checkout_time) {
    if (!checkin_time || !checkout_time) return 0.0;

    time_info_t checkin = parse_time(checkin_time);
    time_info_t checkout = parse_time(checkout_time);

    // 计算实际工作分钟数
    int work_minutes = time_diff_minutes(checkout, checkin);
    double work_hours = work_minutes / 60.0;

    // 加班时长 = 实际工作时长 - 标准工作时长
    double overtime = work_hours - STANDARD_WORK_HOURS;
    return overtime > 0 ? overtime : 0.0;
}

// 用户登录验证函数
int authenticate_user(const char *username, const char *password, int *role) {
    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "无法打开数据库: %s\n", sqlite3_errmsg(db));
        return -1;
    }

    const char *sql = "SELECT id, role FROM users WHERE name = ? AND password = ?";
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "准备语句失败: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        return -1;
    }

    sqlite3_bind_text(stmt, 1, username, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, password, -1, SQLITE_STATIC);

    int user_id = -1;
    rc = sqlite3_step(stmt);
    if (rc == SQLITE_ROW) {
        user_id = sqlite3_column_int(stmt, 0);
        *role = sqlite3_column_int(stmt, 1);
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    return user_id;
}

// 处理登录请求
void handle_login(int client_fd, const char *username, const char *password) {
    char response[BUFFER_SIZE];
    int role;
    int user_id = authenticate_user(username, password, &role);

    if (user_id > 0) {
        snprintf(response, sizeof(response), "登录成功,%d", role);
        log_operation("LOGIN", username, "用户登录成功");
    } else {
        snprintf(response, sizeof(response), "登录失败: 用户名或密码错误");
        log_operation("LOGIN_FAILED", username, "用户登录失败");
    }

    send(client_fd, response, strlen(response), 0);
}

// 处理考勤打卡
void handle_checkin(int client_fd, const char *username) {
    char response[BUFFER_SIZE];
    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);

    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "打卡失败: 数据库错误");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    // 获取用户ID
    const char *get_user_sql = "SELECT id FROM users WHERE name = ?";
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, get_user_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "打卡失败: 查询用户失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }

    sqlite3_bind_text(stmt, 1, username, -1, SQLITE_STATIC);
    rc = sqlite3_step(stmt);
    if (rc != SQLITE_ROW) {
        snprintf(response, sizeof(response), "打卡失败: 用户不存在");
        send(client_fd, response, strlen(response), 0);
        sqlite3_finalize(stmt);
        sqlite3_close(db);
        return;
    }

    int user_id = sqlite3_column_int(stmt, 0);
    sqlite3_finalize(stmt);

    // 获取当前日期
    time_t now;
    time(&now);
    struct tm *tm_info = localtime(&now);
    char date[20];
    strftime(date, sizeof(date), "%Y-%m-%d", tm_info);

    char time_str[20];
    strftime(time_str, sizeof(time_str), "%H:%M:%S", tm_info);

    // 检查今天是否已有记录
    const char *check_sql = "SELECT id, check_in_time FROM attendance WHERE user_id = ? AND date = ?";
    rc = sqlite3_prepare_v2(db, check_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "打卡失败: 查询考勤记录失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }

    sqlite3_bind_int(stmt, 1, user_id);
    sqlite3_bind_text(stmt, 2, date, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    if (rc == SQLITE_ROW) {
        // 今天已有记录，更新上班时间
        const char *existing_checkin = (const char*)sqlite3_column_text(stmt, 1);
        if (existing_checkin && strlen(existing_checkin) > 0) {
            snprintf(response, sizeof(response), "打卡失败: 今天已经上班打卡了 (%s)", existing_checkin);
        } else {
            // 检查是否迟到
            int late_minutes = 0;
            bool late = is_late_checkin(time_str, &late_minutes);

            // 更新上班时间和迟到信息
            sqlite3_finalize(stmt);
            const char *update_sql = "UPDATE attendance SET check_in_time = ?, is_late = ?, late_minutes = ?, status = ? WHERE user_id = ? AND date = ?";
            rc = sqlite3_prepare_v2(db, update_sql, -1, &stmt, NULL);
            if (rc == SQLITE_OK) {
                sqlite3_bind_text(stmt, 1, time_str, -1, SQLITE_STATIC);
                sqlite3_bind_int(stmt, 2, late ? 1 : 0);
                sqlite3_bind_int(stmt, 3, late_minutes);
                sqlite3_bind_text(stmt, 4, late ? "late" : "normal", -1, SQLITE_STATIC);
                sqlite3_bind_int(stmt, 5, user_id);
                sqlite3_bind_text(stmt, 6, date, -1, SQLITE_STATIC);
                rc = sqlite3_step(stmt);
                if (rc == SQLITE_DONE) {
                    if (late) {
                        snprintf(response, sizeof(response), "上班打卡成功: %s %s (迟到%d分钟)",
                                date, time_str, late_minutes);
                        char log_detail[128];
                        snprintf(log_detail, sizeof(log_detail), "%s - 迟到%d分钟", time_str, late_minutes);
                        log_operation("CHECKIN_LATE", username, log_detail);
                    } else {
                        snprintf(response, sizeof(response), "上班打卡成功: %s %s (准时)", date, time_str);
                        log_operation("CHECKIN", username, time_str);
                    }
                } else {
                    snprintf(response, sizeof(response), "打卡失败: 更新记录失败");
                }
            }
        }
    } else {
        // 检查是否迟到
        int late_minutes = 0;
        bool late = is_late_checkin(time_str, &late_minutes);

        // 创建新记录
        sqlite3_finalize(stmt);
        const char *insert_sql = "INSERT INTO attendance (user_id, date, check_in_time, is_late, late_minutes, status) VALUES (?, ?, ?, ?, ?, ?)";
        rc = sqlite3_prepare_v2(db, insert_sql, -1, &stmt, NULL);
        if (rc == SQLITE_OK) {
            sqlite3_bind_int(stmt, 1, user_id);
            sqlite3_bind_text(stmt, 2, date, -1, SQLITE_STATIC);
            sqlite3_bind_text(stmt, 3, time_str, -1, SQLITE_STATIC);
            sqlite3_bind_int(stmt, 4, late ? 1 : 0);
            sqlite3_bind_int(stmt, 5, late_minutes);
            sqlite3_bind_text(stmt, 6, late ? "late" : "normal", -1, SQLITE_STATIC);
            rc = sqlite3_step(stmt);
            if (rc == SQLITE_DONE) {
                if (late) {
                    snprintf(response, sizeof(response), "上班打卡成功: %s %s (迟到%d分钟)",
                            date, time_str, late_minutes);
                    char log_detail[128];
                    snprintf(log_detail, sizeof(log_detail), "%s - 迟到%d分钟", time_str, late_minutes);
                    log_operation("CHECKIN_LATE", username, log_detail);
                } else {
                    snprintf(response, sizeof(response), "上班打卡成功: %s %s (准时)", date, time_str);
                    log_operation("CHECKIN", username, time_str);
                }
            } else {
                snprintf(response, sizeof(response), "打卡失败: 插入记录失败");
            }
        }
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    send(client_fd, response, strlen(response), 0);
}

// 处理下班打卡
void handle_checkout(int client_fd, const char *username) {
    char response[BUFFER_SIZE];
    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);

    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "下班打卡失败: 数据库错误");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    // 获取用户ID
    const char *get_user_sql = "SELECT id FROM users WHERE name = ?";
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, get_user_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "下班打卡失败: 查询用户失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }

    sqlite3_bind_text(stmt, 1, username, -1, SQLITE_STATIC);
    rc = sqlite3_step(stmt);
    if (rc != SQLITE_ROW) {
        snprintf(response, sizeof(response), "下班打卡失败: 用户不存在");
        send(client_fd, response, strlen(response), 0);
        sqlite3_finalize(stmt);
        sqlite3_close(db);
        return;
    }

    int user_id = sqlite3_column_int(stmt, 0);
    sqlite3_finalize(stmt);

    // 获取当前日期和时间
    time_t now;
    time(&now);
    struct tm *tm_info = localtime(&now);
    char date[20];
    strftime(date, sizeof(date), "%Y-%m-%d", tm_info);

    char time_str[20];
    strftime(time_str, sizeof(time_str), "%H:%M:%S", tm_info);

    // 查找今天的考勤记录
    const char *check_sql = "SELECT id, check_in_time, check_out_time FROM attendance WHERE user_id = ? AND date = ?";
    rc = sqlite3_prepare_v2(db, check_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "下班打卡失败: 查询考勤记录失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }

    sqlite3_bind_int(stmt, 1, user_id);
    sqlite3_bind_text(stmt, 2, date, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    if (rc == SQLITE_ROW) {
        const char *checkin_time = (const char*)sqlite3_column_text(stmt, 1);
        const char *existing_checkout = (const char*)sqlite3_column_text(stmt, 2);

        if (!checkin_time || strlen(checkin_time) == 0) {
            snprintf(response, sizeof(response), "下班打卡失败: 今天还没有上班打卡");
        } else if (existing_checkout && strlen(existing_checkout) > 0) {
            snprintf(response, sizeof(response), "下班打卡失败: 今天已经下班打卡了 (%s)", existing_checkout);
        } else {
            // 检查是否早退
            int early_minutes = 0;
            bool early = is_early_leave(time_str, &early_minutes);

            // 计算工作时长
            struct tm checkin_tm = {0};
            struct tm checkout_tm = *tm_info;

            sscanf(checkin_time, "%d:%d:%d", &checkin_tm.tm_hour, &checkin_tm.tm_min, &checkin_tm.tm_sec);
            checkin_tm.tm_year = tm_info->tm_year;
            checkin_tm.tm_mon = tm_info->tm_mon;
            checkin_tm.tm_mday = tm_info->tm_mday;

            time_t checkin_timestamp = mktime(&checkin_tm);
            time_t checkout_timestamp = mktime(&checkout_tm);

            double work_hours = difftime(checkout_timestamp, checkin_timestamp) / 3600.0;
            // 使用新的加班计算函数
            double overtime_hours = calculate_overtime_hours(checkin_time, time_str);

            // 确定考勤状态
            char status[32];
            if (early) {
                strcpy(status, "early_leave");
            } else if (overtime_hours > 0) {
                strcpy(status, "overtime");
            } else {
                strcpy(status, "normal");
            }

            // 更新下班时间、工作时长和早退信息
            sqlite3_finalize(stmt);
            const char *update_sql = "UPDATE attendance SET check_out_time = ?, work_hours = ?, overtime_hours = ?, is_early_leave = ?, early_leave_minutes = ?, status = ? WHERE user_id = ? AND date = ?";
            rc = sqlite3_prepare_v2(db, update_sql, -1, &stmt, NULL);
            if (rc == SQLITE_OK) {
                sqlite3_bind_text(stmt, 1, time_str, -1, SQLITE_STATIC);
                sqlite3_bind_double(stmt, 2, work_hours);
                sqlite3_bind_double(stmt, 3, overtime_hours);
                sqlite3_bind_int(stmt, 4, early ? 1 : 0);
                sqlite3_bind_int(stmt, 5, early_minutes);
                sqlite3_bind_text(stmt, 6, status, -1, SQLITE_STATIC);
                sqlite3_bind_int(stmt, 7, user_id);
                sqlite3_bind_text(stmt, 8, date, -1, SQLITE_STATIC);
                rc = sqlite3_step(stmt);
                if (rc == SQLITE_DONE) {
                    char detail_msg[256];
                    if (early) {
                        snprintf(response, sizeof(response), "下班打卡成功: %s %s (工作%.1f小时, 早退%d分钟)",
                                date, time_str, work_hours, early_minutes);
                        snprintf(detail_msg, sizeof(detail_msg), "%s - 早退%d分钟", time_str, early_minutes);
                        log_operation("CHECKOUT_EARLY", username, detail_msg);
                    } else if (overtime_hours > 0) {
                        snprintf(response, sizeof(response), "下班打卡成功: %s %s (工作%.1f小时, 加班%.1f小时)",
                                date, time_str, work_hours, overtime_hours);
                        snprintf(detail_msg, sizeof(detail_msg), "%s - 加班%.1f小时", time_str, overtime_hours);
                        log_operation("CHECKOUT_OVERTIME", username, detail_msg);
                    } else {
                        snprintf(response, sizeof(response), "下班打卡成功: %s %s (工作%.1f小时, 正常下班)",
                                date, time_str, work_hours);
                        log_operation("CHECKOUT", username, time_str);
                    }
                } else {
                    snprintf(response, sizeof(response), "下班打卡失败: 更新记录失败");
                }
            }
        }
    } else {
        snprintf(response, sizeof(response), "下班打卡失败: 今天没有上班记录");
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    send(client_fd, response, strlen(response), 0);
}

// 处理查询个人考勤记录
void handle_get_attendance(int client_fd, const char *username) {
    char response[BUFFER_SIZE * 2];
    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);

    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "查询失败: 数据库错误");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    const char *sql = "SELECT a.date, a.check_in_time, a.check_out_time, a.work_hours, a.overtime_hours, "
                      "a.is_late, a.late_minutes, a.is_early_leave, a.early_leave_minutes, a.status "
                      "FROM attendance a "
                      "JOIN users u ON a.user_id = u.id "
                      "WHERE u.name = ? "
                      "ORDER BY a.date DESC LIMIT 10";

    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "查询失败: 准备语句失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }

    sqlite3_bind_text(stmt, 1, username, -1, SQLITE_STATIC);

    strcpy(response, "最近10天考勤记录:\n");
    strcat(response, "日期\t\t上班时间\t下班时间\t工作时长\t状态\n");
    strcat(response, "--------------------------------------------------------\n");

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        const char *date = (const char*)sqlite3_column_text(stmt, 0);
        const char *checkin = (const char*)sqlite3_column_text(stmt, 1);
        const char *checkout = (const char*)sqlite3_column_text(stmt, 2);
        double work_hours = sqlite3_column_double(stmt, 3);
        double overtime_hours = sqlite3_column_double(stmt, 4);
        int is_late = sqlite3_column_int(stmt, 5);
        int late_minutes = sqlite3_column_int(stmt, 6);
        int is_early_leave = sqlite3_column_int(stmt, 7);
        int early_leave_minutes = sqlite3_column_int(stmt, 8);
        const char *status = (const char*)sqlite3_column_text(stmt, 9);

        char status_info[128] = "";
        if (is_late && late_minutes > 0) {
            snprintf(status_info, sizeof(status_info), "迟到%d分钟", late_minutes);
        }
        if (is_early_leave && early_leave_minutes > 0) {
            if (strlen(status_info) > 0) strcat(status_info, ",");
            char temp[64];
            snprintf(temp, sizeof(temp), "早退%d分钟", early_leave_minutes);
            strcat(status_info, temp);
        }
        if (overtime_hours > 0) {
            if (strlen(status_info) > 0) strcat(status_info, ",");
            char temp[64];
            snprintf(temp, sizeof(temp), "加班%.1f小时", overtime_hours);
            strcat(status_info, temp);
        }
        if (strlen(status_info) == 0) {
            strcpy(status_info, "正常");
        }

        char line[512];
        snprintf(line, sizeof(line), "%s\t%s\t%s\t%.1f小时\t%s\n",
                date ? date : "N/A",
                checkin ? checkin : "未打卡",
                checkout ? checkout : "未打卡",
                work_hours,
                status_info);
        strcat(response, line);
    }

    if (strlen(response) == strlen("最近10天考勤记录:\n日期\t\t上班时间\t下班时间\t工作时长\t加班时长\n--------------------------------------------------------\n")) {
        strcat(response, "暂无考勤记录\n");
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    send(client_fd, response, strlen(response), 0);
}

// 处理查询加班统计
void handle_get_overtime(int client_fd, const char *username) {
    char response[BUFFER_SIZE];
    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);

    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "查询失败: 数据库错误");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    // 查询本月考勤统计
    const char *sql = "SELECT COUNT(*) as days, SUM(overtime_hours) as total_overtime, "
                      "SUM(is_late) as late_days, SUM(late_minutes) as total_late_minutes, "
                      "SUM(is_early_leave) as early_days, SUM(early_leave_minutes) as total_early_minutes "
                      "FROM attendance a "
                      "JOIN users u ON a.user_id = u.id "
                      "WHERE u.name = ? AND strftime('%Y-%m', a.date) = strftime('%Y-%m', 'now')";

    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "查询失败: 准备语句失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }

    sqlite3_bind_text(stmt, 1, username, -1, SQLITE_STATIC);

    if (sqlite3_step(stmt) == SQLITE_ROW) {
        int days = sqlite3_column_int(stmt, 0);
        double total_overtime = sqlite3_column_double(stmt, 1);
        int late_days = sqlite3_column_int(stmt, 2);
        int total_late_minutes = sqlite3_column_int(stmt, 3);
        int early_days = sqlite3_column_int(stmt, 4);
        int total_early_minutes = sqlite3_column_int(stmt, 5);

        snprintf(response, sizeof(response),
                "本月考勤统计:\n"
                "出勤天数: %d 天\n"
                "总加班时长: %.1f 小时\n"
                "平均每天加班: %.1f 小时\n"
                "迟到天数: %d 天\n"
                "总迟到时长: %d 分钟\n"
                "早退天数: %d 天\n"
                "总早退时长: %d 分钟\n"
                "出勤率: %.1f%%\n",
                days, total_overtime, days > 0 ? total_overtime / days : 0.0,
                late_days, total_late_minutes,
                early_days, total_early_minutes,
                days > 0 ? ((double)(days - late_days - early_days) / days * 100) : 0.0);
    } else {
        snprintf(response, sizeof(response), "本月暂无考勤记录");
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    send(client_fd, response, strlen(response), 0);
}

// 验证管理员权限
bool verify_admin(const char *username) {
    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);
    if (rc != SQLITE_OK) {
        return false;
    }

    const char *sql = "SELECT role FROM users WHERE name = ?";
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_close(db);
        return false;
    }

    sqlite3_bind_text(stmt, 1, username, -1, SQLITE_STATIC);

    bool is_admin = false;
    if (sqlite3_step(stmt) == SQLITE_ROW) {
        int role = sqlite3_column_int(stmt, 0);
        is_admin = (role == 0);
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    return is_admin;
}

// 处理管理员添加用户
void handle_admin_add_user(int client_fd, const char *admin_user, const char *new_username, const char *new_password, int role) {
    char response[BUFFER_SIZE];

    if (!verify_admin(admin_user)) {
        snprintf(response, sizeof(response), "操作失败: 权限不足");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "操作失败: 数据库错误");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    const char *sql = "INSERT INTO users (name, password, role) VALUES (?, ?, ?)";
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "操作失败: 准备语句失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }

    sqlite3_bind_text(stmt, 1, new_username, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, new_password, -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 3, role);

    rc = sqlite3_step(stmt);
    if (rc == SQLITE_DONE) {
        snprintf(response, sizeof(response), "添加用户成功: %s (角色: %s)",
                new_username, role == 0 ? "管理员" : "普通员工");
        log_operation("ADD_USER", admin_user, new_username);
    } else {
        snprintf(response, sizeof(response), "添加用户失败: 用户名可能已存在");
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    send(client_fd, response, strlen(response), 0);
}

// 处理管理员删除用户
void handle_admin_delete_user(int client_fd, const char *admin_user, const char *target_user) {
    char response[BUFFER_SIZE];

    if (!verify_admin(admin_user)) {
        snprintf(response, sizeof(response), "操作失败: 权限不足");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "操作失败: 数据库错误");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    // 先删除考勤记录
    const char *delete_attendance_sql = "DELETE FROM attendance WHERE user_id = (SELECT id FROM users WHERE name = ?)";
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, delete_attendance_sql, -1, &stmt, NULL);
    if (rc == SQLITE_OK) {
        sqlite3_bind_text(stmt, 1, target_user, -1, SQLITE_STATIC);
        sqlite3_step(stmt);
        sqlite3_finalize(stmt);
    }

    // 删除用户
    const char *delete_user_sql = "DELETE FROM users WHERE name = ?";
    rc = sqlite3_prepare_v2(db, delete_user_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "操作失败: 准备语句失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }

    sqlite3_bind_text(stmt, 1, target_user, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    if (rc == SQLITE_DONE && sqlite3_changes(db) > 0) {
        snprintf(response, sizeof(response), "删除用户成功: %s", target_user);
        log_operation("DELETE_USER", admin_user, target_user);
    } else {
        snprintf(response, sizeof(response), "删除用户失败: 用户不存在");
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    send(client_fd, response, strlen(response), 0);
}

// 处理管理员查询用户列表
void handle_admin_get_users(int client_fd, const char *admin_user) {
    char response[BUFFER_SIZE * 2];

    if (!verify_admin(admin_user)) {
        snprintf(response, sizeof(response), "操作失败: 权限不足");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "操作失败: 数据库错误");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    const char *sql = "SELECT id, name, role FROM users ORDER BY id";
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "操作失败: 准备语句失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }

    strcpy(response, "用户列表:\nID\t用户名\t\t角色\n");
    strcat(response, "--------------------------------\n");

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        int id = sqlite3_column_int(stmt, 0);
        const char *name = (const char*)sqlite3_column_text(stmt, 1);
        int role = sqlite3_column_int(stmt, 2);

        char line[128];
        snprintf(line, sizeof(line), "%d\t%s\t\t%s\n",
                id, name, role == 0 ? "管理员" : "普通员工");
        strcat(response, line);
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    send(client_fd, response, strlen(response), 0);
}

// 处理管理员查询特定用户考勤记录
void handle_admin_get_user_attendance(int client_fd, const char *admin_user, const char *target_user) {
    char response[BUFFER_SIZE * 2];

    if (!verify_admin(admin_user)) {
        snprintf(response, sizeof(response), "操作失败: 权限不足");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "操作失败: 数据库错误");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    const char *sql = "SELECT a.date, a.check_in_time, a.check_out_time, a.work_hours, a.overtime_hours, "
                      "a.is_late, a.late_minutes, a.is_early_leave, a.early_leave_minutes, a.status "
                      "FROM attendance a "
                      "JOIN users u ON a.user_id = u.id "
                      "WHERE u.name = ? "
                      "ORDER BY a.date DESC LIMIT 20";

    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "查询失败: 准备语句失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }
    
    sqlite3_bind_text(stmt, 1, target_user, -1, SQLITE_STATIC);

    strcpy(response, "日期\t\t上班时间\t下班时间\t工作时长\t状态\n");
    strcat(response, "--------------------------------------------------------\n");

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        const char *date = (const char*)sqlite3_column_text(stmt, 0);
        const char *checkin = (const char*)sqlite3_column_text(stmt, 1);
        const char *checkout = (const char*)sqlite3_column_text(stmt, 2);
        double work_hours = sqlite3_column_double(stmt, 3);
        double overtime_hours = sqlite3_column_double(stmt, 4);
        int is_late = sqlite3_column_int(stmt, 5);
        int late_minutes = sqlite3_column_int(stmt, 6);
        int is_early_leave = sqlite3_column_int(stmt, 7);
        int early_leave_minutes = sqlite3_column_int(stmt, 8);

        char status_info[128] = "";
        if (is_late && late_minutes > 0) {
            snprintf(status_info, sizeof(status_info), "迟到%d分钟", late_minutes);
        }
        if (is_early_leave && early_leave_minutes > 0) {
            if (strlen(status_info) > 0) strcat(status_info, ",");
            char temp[64];
            snprintf(temp, sizeof(temp), "早退%d分钟", early_leave_minutes);
            strcat(status_info, temp);
        }
        if (overtime_hours > 0) {
            if (strlen(status_info) > 0) strcat(status_info, ",");
            char temp[64];
            snprintf(temp, sizeof(temp), "加班%.1f小时", overtime_hours);
            strcat(status_info, temp);
        }
        if (strlen(status_info) == 0) {
            strcpy(status_info, "正常");
        }

        char line[512];
        snprintf(line, sizeof(line), "%s\t%s\t%s\t%.1f小时\t%s\n",
                date ? date : "N/A",
                checkin ? checkin : "未打卡",
                checkout ? checkout : "未打卡",
                work_hours,
                status_info);
        strcat(response, line);
    }

    if (strlen(response) == strlen("日期\t\t上班时间\t下班时间\t工作时长\t状态\n--------------------------------------------------------\n")) {
        strcat(response, "暂无考勤记录\n");
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    send(client_fd, response, strlen(response), 0);
}

// 处理管理员查询所有用户考勤记录
void handle_admin_get_all_attendance(int client_fd, const char *admin_user) {
    char response[BUFFER_SIZE * 4];

    if (!verify_admin(admin_user)) {
        snprintf(response, sizeof(response), "操作失败: 权限不足");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    sqlite3 *db;
    int rc = sqlite3_open("users.db", &db);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "操作失败: 数据库错误");
        send(client_fd, response, strlen(response), 0);
        return;
    }

    const char *sql = "SELECT u.name, a.date, a.check_in_time, a.check_out_time, a.work_hours, "
                      "a.is_late, a.late_minutes, a.is_early_leave, a.early_leave_minutes "
                      "FROM attendance a "
                      "JOIN users u ON a.user_id = u.id "
                      "ORDER BY a.date DESC, u.name LIMIT 50";

    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        snprintf(response, sizeof(response), "查询失败: 准备语句失败");
        send(client_fd, response, strlen(response), 0);
        sqlite3_close(db);
        return;
    }

    strcpy(response, "用户名\t\t日期\t\t上班时间\t下班时间\t工作时长\t状态\n");
    strcat(response, "------------------------------------------------------------------------\n");

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        const char *name = (const char*)sqlite3_column_text(stmt, 0);
        const char *date = (const char*)sqlite3_column_text(stmt, 1);
        const char *checkin = (const char*)sqlite3_column_text(stmt, 2);
        const char *checkout = (const char*)sqlite3_column_text(stmt, 3);
        double work_hours = sqlite3_column_double(stmt, 4);
        int is_late = sqlite3_column_int(stmt, 5);
        int late_minutes = sqlite3_column_int(stmt, 6);
        int is_early_leave = sqlite3_column_int(stmt, 7);
        int early_leave_minutes = sqlite3_column_int(stmt, 8);

        char status_info[128] = "正常";
        if (is_late && late_minutes > 0) {
            snprintf(status_info, sizeof(status_info), "迟到%d分钟", late_minutes);
        }
        if (is_early_leave && early_leave_minutes > 0) {
            if (is_late) strcat(status_info, ",");
            else strcpy(status_info, "");
            char temp[64];
            snprintf(temp, sizeof(temp), "早退%d分钟", early_leave_minutes);
            strcat(status_info, temp);
        }

        char line[512];
        snprintf(line, sizeof(line), "%s\t\t%s\t%s\t%s\t%.1f小时\t%s\n",
                name ? name : "N/A",
                date ? date : "N/A",
                checkin ? checkin : "未打卡",
                checkout ? checkout : "未打卡",
                work_hours,
                status_info);
        strcat(response, line);
    }

    if (strlen(response) == strlen("用户名\t\t日期\t\t上班时间\t下班时间\t工作时长\t状态\n------------------------------------------------------------------------\n")) {
        strcat(response, "暂无考勤记录\n");
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    send(client_fd, response, strlen(response), 0);
}
// 提取原有的命令处理逻辑到单独函数
void process_single_command(int client_fd, char *command_buffer) {
    char *command = strtok(command_buffer, ",");
    if (command == NULL) {
        send(client_fd, "错误: 无效的命令格式", 24, 0);
        return;
    }

    if (strcmp(command, "login") == 0) {
        char *username = strtok(NULL, ",");
        char *password = strtok(NULL, ",");
        if (username && password) {
            handle_login(client_fd, username, password);
        } else {
            send(client_fd, "错误: 登录参数不完整", 24, 0);
        }
    } else if (strcmp(command, "checkin") == 0) {
        char *username = strtok(NULL, ",");
        if (username) {
            handle_checkin(client_fd, username);
        } else {
            send(client_fd, "错误: 打卡参数不完整", 24, 0);
        }
    } else if (strcmp(command, "checkout") == 0) {
        char *username = strtok(NULL, ",");
        if (username) {
            handle_checkout(client_fd, username);
        } else {
            send(client_fd, "错误: 打卡参数不完整", 24, 0);
        }
    } else if (strcmp(command, "get_attendance") == 0) {
        char *username = strtok(NULL, ",");
        if (username) {
            handle_get_attendance(client_fd, username);
        } else {
            send(client_fd, "错误: 查询参数不完整", 24, 0);
        }
    } else if (strcmp(command, "get_overtime") == 0) {
        char *username = strtok(NULL, ",");
        if (username) {
            handle_get_overtime(client_fd, username);
        } else {
            send(client_fd, "错误: 查询参数不完整", 24, 0);
        }
    } else if (strcmp(command, "admin_add_user") == 0) {
        char *admin_user = strtok(NULL, ",");
        char *new_username = strtok(NULL, ",");
        char *new_password = strtok(NULL, ",");
        char *role_str = strtok(NULL, ",");
        if (admin_user && new_username && new_password && role_str) {
            handle_admin_add_user(client_fd, admin_user, new_username, new_password, atoi(role_str));
        } else {
            send(client_fd, "错误: 添加用户参数不完整", 30, 0);
        }
    } else if (strcmp(command, "admin_delete_user") == 0) {
        char *admin_user = strtok(NULL, ",");
        char *target_user = strtok(NULL, ",");
        if (admin_user && target_user) {
            handle_admin_delete_user(client_fd, admin_user, target_user);
        } else {
            send(client_fd, "错误: 删除用户参数不完整", 30, 0);
        }
    } else if (strcmp(command, "admin_get_users") == 0) {
        char *admin_user = strtok(NULL, ",");
        if (admin_user) {
            handle_admin_get_users(client_fd, admin_user);
        } else {
            send(client_fd, "错误: 查询用户参数不完整", 30, 0);
        }
    } else if (strcmp(command, "admin_get_user_attendance") == 0) {
        char *admin_user = strtok(NULL, ",");
        char *target_user = strtok(NULL, ",");
        if (admin_user && target_user) {
            handle_admin_get_user_attendance(client_fd, admin_user, target_user);
        } else {
            send(client_fd, "错误: 查询用户考勤参数不完整", 30, 0);
        }
    } else if (strcmp(command, "admin_get_all_attendance") == 0) {
        char *admin_user = strtok(NULL, ",");
        if (admin_user) {
            handle_admin_get_all_attendance(client_fd, admin_user);
        } else {
            send(client_fd, "错误: 查询所有用户考勤参数不完整", 30, 0);
        }
    } else {
        send(client_fd, "错误: 未知命令", 18, 0);
    }
}

// 客户端处理线程函数
void* handle_client(void* arg) {
    client_info_t *client = (client_info_t*)arg;
    int client_fd = client->socket_fd;
    char buffer[BUFFER_SIZE];
    ssize_t count;

    printf("新客户端连接: %s:%d\n",
           inet_ntoa(client->address.sin_addr),
           ntohs(client->address.sin_port));

    while (true) {
        // 清空缓冲区
        memset(buffer, 0, BUFFER_SIZE);
        
        count = recv(client_fd, buffer, BUFFER_SIZE - 1, 0);
        if (count > 0) {
            buffer[count] = '\0';
            printf("接收到客户端数据: %s\n", buffer);

            // 处理可能的多个命令（用换行符分割）
            char *command_start = buffer;
            char *command_end;
            
            while ((command_end = strchr(command_start, '\n')) != NULL) {
                *command_end = '\0';
                process_single_command(client_fd, command_start);
                command_start = command_end + 1;
            }
            
            // 处理最后一个命令（如果没有换行符）
            if (strlen(command_start) > 0) {
                process_single_command(client_fd, command_start);
            }
            
            // 添加小延迟，避免响应粘包
            usleep(10000); // 10ms延迟
            
        } else if (count == 0) {
            printf("客户端断开连接\n");
            break;
        } else {
            perror("接收数据失败");
            break;
        }
    }

    close(client_fd);
    free(client);
    return NULL;
}


int main(int argc, char *argv[]) {
    // 初始化数据库
/*    if (init_database() != 0) {
        fprintf(stderr, "数据库初始化失败\n");
        exit(EXIT_FAILURE);
    }
*/
    // 创建套接字
    int server_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (server_fd == -1) {
        perror("创建套接字失败");
        exit(EXIT_FAILURE);
    }

    // 设置套接字选项，允许重用地址
    int opt = 1;
    if (setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        perror("设置套接字选项失败");
        close(server_fd);
        exit(EXIT_FAILURE);
    }

    // 绑定端口号
    struct sockaddr_in server_addr = {0};
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(PORT);
    server_addr.sin_addr.s_addr = INADDR_ANY;

    if (bind(server_fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) == -1) {
        perror("绑定端口失败");
        close(server_fd);
        exit(EXIT_FAILURE);
    }

    // 监听端口号
    if (listen(server_fd, MAX_CLIENTS) == -1) {
        perror("监听端口失败");
        close(server_fd);
        exit(EXIT_FAILURE);
    }

    printf("员工考勤系统服务器启动成功，监听端口 %d\n", PORT);
    printf("等待客户端连接...\n");

    // 主循环，接受客户端连接
    while (true) {
        client_info_t *client = malloc(sizeof(client_info_t));
        socklen_t client_addr_len = sizeof(client->address);

        client->socket_fd = accept(server_fd, (struct sockaddr *)&client->address, &client_addr_len);
        if (client->socket_fd == -1) {
            perror("接受客户端连接失败");
            free(client);
            continue;
        }

        // 创建新线程处理客户端
        pthread_t thread_id;
        if (pthread_create(&thread_id, NULL, handle_client, client) != 0) {
            perror("创建线程失败");
            close(client->socket_fd);
            free(client);
            continue;
        }

        // 分离线程，让其自动清理资源
        pthread_detach(thread_id);
    }

    close(server_fd);
    return 0;
}

